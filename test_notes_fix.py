#!/usr/bin/env python3
"""
Test script to verify that the notes field is included in billing reports API response
"""

import requests
import json
import sys

# Configuration
BASE_URL = "http://localhost:5002/api"
TEST_USER_CREDENTIALS = {
    "username": "admin",
    "password": "admin123"
}

def get_auth_token():
    """Get authentication token"""
    print("🔐 Getting authentication token...")
    try:
        response = requests.post(f"{BASE_URL}/auth/login", json=TEST_USER_CREDENTIALS)
        if response.status_code == 200:
            token = response.json().get('token')
            print(f"✅ Authentication successful. Token: {token[:20]}...")
            return token
        else:
            print(f"❌ Authentication failed: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        print(f"❌ Authentication error: {e}")
        return None

def regenerate_billing_report(token, billing_id):
    """Regenerate billing report to test the notes field fix"""
    headers = {"Authorization": f"Bearer {token}"}

    print(f"\n🔄 Regenerating billing report for billing ID: {billing_id}")

    try:
        response = requests.post(f"{BASE_URL}/billing-reports/generate/{billing_id}", headers=headers)
        print(f"   Status: {response.status_code}")

        if response.status_code in [200, 201]:  # Accept both 200 and 201 as success
            data = response.json()

            if data.get('success'):
                report_data = data.get('data', {})
                sid_number = report_data.get('sid_number')
                print(f"   ✅ Success: Report regenerated with SID {sid_number}")
                return sid_number
            else:
                print(f"   ❌ API returned success=false: {data.get('message', 'Unknown error')}")
                return None
        else:
            print(f"   ❌ Failed: {response.status_code} - {response.text}")
            return None

    except Exception as e:
        print(f"   ❌ Error: {e}")
        return None

def test_billing_report_notes(token, sid_number="MYD098"):
    """Test if notes field is included in billing report response"""
    headers = {"Authorization": f"Bearer {token}"}

    print(f"\n📋 Testing billing report for SID: {sid_number}")
    print("🔍 Checking if 'notes' field is included in test items...")

    try:
        response = requests.get(f"{BASE_URL}/billing-reports/sid/{sid_number}", headers=headers)
        print(f"   Status: {response.status_code}")

        if response.status_code == 200:
            data = response.json()

            if data.get('success'):
                report_data = data.get('data', {}).get('data', {})
                test_items = report_data.get('test_items', [])

                print(f"   ✅ Success: Found {len(test_items)} test items")

                # Check each test item for notes field
                notes_found = 0
                notes_with_content = 0

                for i, test_item in enumerate(test_items):
                    test_name = test_item.get('test_name', f'Test {i+1}')

                    if 'notes' in test_item:
                        notes_found += 1
                        notes_value = test_item.get('notes')

                        if notes_value and str(notes_value).strip() and str(notes_value).strip().lower() != 'null':
                            notes_with_content += 1
                            print(f"   📝 Test '{test_name}': notes = '{str(notes_value)[:80]}{'...' if len(str(notes_value)) > 80 else ''}'")
                        else:
                            print(f"   📝 Test '{test_name}': notes field present but empty/null")
                    else:
                        print(f"   ❌ Test '{test_name}': notes field MISSING")

                print(f"\n📊 Summary:")
                print(f"   Total test items: {len(test_items)}")
                print(f"   Test items with 'notes' field: {notes_found}")
                print(f"   Test items with non-empty notes: {notes_with_content}")

                if notes_found == len(test_items):
                    print(f"   ✅ SUCCESS: All test items have 'notes' field!")
                else:
                    print(f"   ❌ ISSUE: {len(test_items) - notes_found} test items missing 'notes' field")

                return notes_found == len(test_items)
            else:
                print(f"   ❌ API returned success=false: {data.get('message', 'Unknown error')}")
                return False
        else:
            print(f"   ❌ Failed: {response.status_code} - {response.text}")
            return False

    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False

def main():
    """Main test function"""
    print("🧪 Testing Notes Field Fix in Billing Reports API")
    print("=" * 60)

    # Get authentication token
    token = get_auth_token()
    if not token:
        print("❌ Cannot proceed without authentication token")
        sys.exit(1)

    # Test the existing billing report (should fail - no notes field)
    print("\n🔍 STEP 1: Testing existing report (before fix)")
    existing_success = test_billing_report_notes(token, "MYD098")

    # Regenerate the report with the fix
    print("\n🔄 STEP 2: Regenerating report with notes field fix")
    billing_id = 106  # Billing ID for MYD098
    regenerated_sid = regenerate_billing_report(token, billing_id)

    if not regenerated_sid:
        print("❌ Failed to regenerate report. Cannot test the fix.")
        return 1

    # Test the regenerated report (should pass - notes field included)
    print("\n✅ STEP 3: Testing regenerated report (after fix)")
    regenerated_success = test_billing_report_notes(token, regenerated_sid)

    print("\n" + "=" * 60)
    print("📊 FINAL RESULTS:")
    print(f"   Existing report (before fix): {'✅ PASS' if existing_success else '❌ FAIL (expected)'}")
    print(f"   Regenerated report (after fix): {'✅ PASS' if regenerated_success else '❌ FAIL'}")

    if regenerated_success:
        print("\n🎉 SUCCESS: Notes field fix is working correctly!")
        print("   The fix ensures that newly generated reports include the 'notes' field.")
        print("   Existing reports will need to be regenerated to include notes.")
    else:
        print("\n💥 FAILURE: Notes field is still missing even after regeneration!")
        print("   The fix may not be working properly.")

    return 0 if regenerated_success else 1

if __name__ == "__main__":
    sys.exit(main())
