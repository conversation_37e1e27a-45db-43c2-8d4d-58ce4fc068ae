#!/usr/bin/env python3
"""
Debug script to examine the raw API response structure
"""

import requests
import json
import sys

# Configuration
BASE_URL = "http://localhost:5002/api"
TEST_USER_CREDENTIALS = {
    "username": "admin",
    "password": "admin123"
}

def get_auth_token():
    """Get authentication token"""
    try:
        response = requests.post(f"{BASE_URL}/auth/login", json=TEST_USER_CREDENTIALS)
        if response.status_code == 200:
            return response.json().get('token')
        return None
    except Exception as e:
        print(f"Auth error: {e}")
        return None

def debug_api_response(token, sid_number="MYD101"):
    """Debug the raw API response structure"""
    headers = {"Authorization": f"Bearer {token}"}
    
    print(f"🔍 Debugging API response for SID: {sid_number}")
    
    try:
        response = requests.get(f"{BASE_URL}/billing-reports/sid/{sid_number}", headers=headers)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            
            # Print the full structure
            print("\n📋 Full API Response Structure:")
            print(json.dumps(data, indent=2)[:2000] + "..." if len(json.dumps(data, indent=2)) > 2000 else json.dumps(data, indent=2))
            
            # Focus on test items
            if data.get('success'):
                report_data = data.get('data', {}).get('data', {})
                test_items = report_data.get('test_items', [])
                
                print(f"\n🧪 Test Items Analysis:")
                print(f"Total test items: {len(test_items)}")
                
                if test_items:
                    first_test = test_items[0]
                    print(f"\n📝 First test item fields:")
                    for key in sorted(first_test.keys()):
                        value = first_test[key]
                        if isinstance(value, str) and len(value) > 100:
                            print(f"  {key}: '{value[:100]}...'")
                        else:
                            print(f"  {key}: {value}")
                    
                    # Check specifically for notes field
                    if 'notes' in first_test:
                        print(f"\n✅ 'notes' field found in first test!")
                        print(f"   Value: {first_test['notes']}")
                    else:
                        print(f"\n❌ 'notes' field NOT found in first test")
                        print(f"   Available fields: {list(first_test.keys())}")
                
        else:
            print(f"❌ Failed: {response.status_code} - {response.text}")
            
    except Exception as e:
        print(f"❌ Error: {e}")

def main():
    """Main debug function"""
    print("🐛 Debugging Notes Field in Billing Reports API")
    print("=" * 60)
    
    # Get authentication token
    token = get_auth_token()
    if not token:
        print("❌ Cannot proceed without authentication token")
        sys.exit(1)
    
    # Debug the API response
    debug_api_response(token)

if __name__ == "__main__":
    main()
